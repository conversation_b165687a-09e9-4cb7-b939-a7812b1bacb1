import { Router } from 'express';
import { healthController } from '../controllers/healthController.js';

/**
 * Health routes interface
 * Follows Interface Segregation Principle
 */
export interface IHealthRoutes {
  getRouter(): Router;
}

/**
 * Health routes implementation
 * Follows Single Responsibility Principle - handles only health-related routes
 */
export class HealthRoutes implements IHealthRoutes {
  private readonly router: Router;

  constructor() {
    this.router = Router();
    this.initializeRoutes();
  }

  /**
   * Initialize health routes
   * Follows Open/Closed Principle - can be extended without modification
   */
  private initializeRoutes(): void {
    /**
     * @swagger
     * tags:
     *   name: Health
     *   description: API health monitoring endpoints
     */
    
    // Health check endpoint
    this.router.get('/', healthController.checkHealth.bind(healthController));
  }

  /**
   * Get configured router instance
   * @returns Express Router instance
   */
  public getRouter(): Router {
    return this.router;
  }
}

/**
 * Factory function for creating health routes
 * Implements Dependency Inversion Principle
 */
export const createHealthRoutes = (): IHealthRoutes => {
  return new HealthRoutes();
};

// Export router instance for convenience
const healthRoutes = createHealthRoutes();
export default healthRoutes.getRouter();