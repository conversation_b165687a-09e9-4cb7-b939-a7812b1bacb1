{"name": "lion_car_rentals_backend", "version": "1.0.0", "description": "Modular MVC folder structure for a Node.js + Express + Prisma REST API project\r ------------------------------------------------------------------------------", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec \"tsx\" src/server.ts", "dev:watch": "tsc --watch"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.19.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^16.18.108", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "prisma": "^5.19.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.3.3"}}