# LION_CAR_RENTALS_BACKEND

Modular MVC folder structure for a TypeScript + Node.js + Express + Prisma REST API project
-------------------------------------------------------------------------------------------

Modular MVC Folder Structure
----------------------------
`
project-root/
├── prisma/
│   └── schema.prisma              # Prisma schema
│   └── migrations/
│
├── src/
│   ├── config/
│   │   └── db.ts                  # Database connection
│  
│
│   ├── modules/
│   │   └── customsInvoice/
│   │       ├── customsInvoice.model.ts       # Prisma DB queries
│   │       ├── customsInvoice.service.ts     # Business logic
│   │       ├── customsInvoice.controller.ts  # Handles req/res
│   │       ├── customsInvoice.route.ts       # Express router
│   │       
│   │
│   │   └── auth/
│   │       ├── auth.model.ts
│   │       ├── auth.service.ts
│   │       ├── auth.controller.ts
│   │       ├── auth.route.ts
│   │    
│   │
│   │   └── user/
│   │       ├── user.model.ts
│   │       ├── user.service.ts
│   │       ├── user.controller.ts
│   │       ├── user.route.ts
│   │     
│
│   ├── middleware/
│   │   ├── authMiddleware.ts
│   │   └── errorHandler.ts
│
│   ├── utils/
│   │   ├── logger.ts
│   │   └── helperFunctions.ts
│
│   ├── routes/
│   │   └── index.ts               # Combines all module routes
│
│   ├── types/
│   │   └── environment.d.ts       # Environment variable types
│
│   └── server.ts                  # App entry point
│
├── uploads/                      # Static/media uploads
├── logs/                         # Logs
├── dist/                         # TypeScript build output
├── .env
├── .gitignore
├── tsconfig.json                 # TypeScript configuration
├── nodemon.json                  # Nodemon configuration
├── package.json
└── package-lock.json
`

## Development Setup

### Prerequisites
- Node.js (v16.18 or higher)
- npm or yarn
- PostgreSQL database

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database configuration
```

3. Generate Prisma client:
```bash
npx prisma generate
```

4. Run database migrations:
```bash
npx prisma migrate dev
```

### Development Commands

- **Development server with hot reload:**
  ```bash
  npm run dev
  ```

- **Build TypeScript to JavaScript:**
  ```bash
  npm run build
  ```

- **Start production server:**
  ```bash
  npm start
  ```

- **Watch mode for TypeScript compilation:**
  ```bash
  npm run dev:watch
  ```

### TypeScript Features

- **Type Safety:** Full TypeScript support with strict type checking
- **Environment Types:** Type-safe environment variable access
- **ES Modules:** Modern import/export syntax
- **Source Maps:** Debug support with source maps
- **Hot Reload:** Development server with automatic restart on file changes

## API Documentation

### Swagger Integration

This project includes comprehensive API documentation using Swagger/OpenAPI 3.0:

- **Interactive Documentation:** `http://localhost:5001/api-docs`
- **JSON Schema:** `http://localhost:5001/api-docs.json`
- **API Health Check:** `http://localhost:5001/api/health`

### Features

- **📚 Interactive API Documentation** - Swagger UI with try-it-out functionality
- **🔒 Security Schemas** - JWT Bearer token and API key authentication
- **📊 Rate Limiting** - 100 requests per 15-minute window
- **📝 Request Logging** - Comprehensive request/response logging
- **✅ Input Validation** - Schema-based request validation
- **🏗️ SOLID Principles** - Clean architecture following SOLID principles
- **🔄 Error Handling** - Standardized error responses
- **📄 Pagination Support** - Consistent pagination for list endpoints

### API Endpoints

#### Health & Monitoring
- `GET /api/health` - API health status
- `GET /api` - API information and links

#### Future Endpoints (Schema Ready)
- `GET /api/users` - User management
- `GET /api/cars` - Car inventory management
- `GET /api/rentals` - Rental operations

### Authentication

The API supports multiple authentication methods:

```http
# JWT Bearer Token
Authorization: Bearer <your-jwt-token>

# API Key (Optional)
X-API-Key: <your-api-key>
```

### Response Format

All API responses follow a consistent structure:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Rate Limiting

API requests are rate-limited with headers:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

### Environment Variables

```env
# Server Configuration
PORT=5001
NODE_ENV=development

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database"

# API Security (Optional)
API_KEY="your-api-key"
JWT_SECRET="your-jwt-secret"
```

### Architecture Principles

This project follows **SOLID principles** for maintainable code:

- **S**ingle Responsibility Principle
- **O**pen/Closed Principle
- **L**iskov Substitution Principle
- **I**nterface Segregation Principle
- **D**ependency Inversion Principle

### Testing the API

1. **Swagger UI (Recommended):**
   - Visit `http://localhost:5001/api-docs`
   - Use the interactive interface to test endpoints

2. **curl Example:**
   ```bash
   curl -X GET "http://localhost:5001/api/health" \
     -H "accept: application/json"
   ```

3. **Postman:**
   - Import the OpenAPI schema from `/api-docs.json`

### Project Structure Updates

```
src/
├── config/
│   ├── db.ts
│   └── swagger.ts              # Swagger configuration
├── controllers/
│   └── healthController.ts     # Health check controller
├── middleware/
│   ├── errorHandler.ts
│   └── apiMiddleware.ts        # Rate limiting & validation
├── routes/
│   ├── index.ts
│   └── healthRoutes.ts         # Health check routes
├── docs/
│   ├── apiSchemas.ts           # Swagger schemas
│   └── README.md               # API documentation
└── types/
    └── environment.d.ts
```