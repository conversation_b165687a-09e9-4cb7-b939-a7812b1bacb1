import { Request, Response } from 'express';
import { registerUserService, loginUserService } from './auth.service';

export const registerUser = async (req: Request, res: Response) => {
    try {
        const result = await registerUserService(req.body);
        // Don't send the password back in the response
        const { password, ...userWithoutPassword } = result.user;
        res.status(201).json({ success: true, message: 'User registered successfully', data: { user: userWithoutPassword, token: result.token } });
    } catch (error: any) {
        res.status(400).json({ success: false, message: error.message });
    }
};

export const loginUser = async (req: Request, res: Response) => {
    try {
        const result = await loginUserService(req.body);
        // Don't send the password back in the response
        const { password, ...userWithoutPassword } = result.user;
        res.status(200).json({ success: true, message: 'Login successful', data: { user: userWithoutPassword, token: result.token } });
    } catch (error: any) {
        res.status(400).json({ success: false, message: error.message });
    }
};