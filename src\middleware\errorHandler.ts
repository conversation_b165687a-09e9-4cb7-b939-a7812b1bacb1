import { Request, Response, NextFunction } from "express";

interface ErrorResponse {
  success: boolean;
  message: string;
  error: {
    code: number;
    type: string;
    details: string;
  };
}

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error("Error:", err.message || err);

  const errorResponse: ErrorResponse = {
    success: false,
    message: "Internal Server Error",
    error: {
      code: 500,
      type: "SERVER_ERROR",
      details: err.message || "Something went wrong!"
    }
  };

  res.status(500).json(errorResponse);
};
