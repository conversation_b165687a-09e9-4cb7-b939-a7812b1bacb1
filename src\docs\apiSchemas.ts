/**
 * API Schema definitions for Swagger documentation
 * Follows Single Responsibility Principle - handles only schema definitions
 * Implements Interface Segregation Principle with specific interfaces
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - email
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique user identifier
 *           example: "123e4567-e89b-12d3-a456-************"
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *           example: "<EMAIL>"
 *         name:
 *           type: string
 *           description: User's full name
 *           example: "<PERSON>"
 *         phone:
 *           type: string
 *           description: User's phone number
 *           example: "+**********"
 *         dateOfBirth:
 *           type: string
 *           format: date
 *           description: User's date of birth
 *           example: "1990-01-01"
 *         licenseNumber:
 *           type: string
 *           description: Driver's license number
 *           example: "DL123456789"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Account creation timestamp
 *           example: "2024-01-01T00:00:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *           example: "2024-01-01T00:00:00.000Z"
 *
 *     Car:
 *       type: object
 *       required:
 *         - make
 *         - model
 *         - year
 *         - licensePlate
 *         - pricePerDay
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique car identifier
 *           example: "123e4567-e89b-12d3-a456-************"
 *         make:
 *           type: string
 *           description: Car manufacturer
 *           example: "Toyota"
 *         model:
 *           type: string
 *           description: Car model
 *           example: "Camry"
 *         year:
 *           type: integer
 *           minimum: 1900
 *           maximum: 2030
 *           description: Manufacturing year
 *           example: 2023
 *         licensePlate:
 *           type: string
 *           description: Vehicle license plate
 *           example: "ABC-1234"
 *         color:
 *           type: string
 *           description: Car color
 *           example: "Blue"
 *         fuelType:
 *           type: string
 *           enum: ["gasoline", "diesel", "electric", "hybrid"]
 *           description: Type of fuel
 *           example: "gasoline"
 *         transmission:
 *           type: string
 *           enum: ["manual", "automatic"]
 *           description: Transmission type
 *           example: "automatic"
 *         seats:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           description: Number of seats
 *           example: 5
 *         pricePerDay:
 *           type: number
 *           format: float
 *           minimum: 0
 *           description: Daily rental price in USD
 *           example: 49.99
 *         isAvailable:
 *           type: boolean
 *           description: Availability status
 *           example: true
 *         mileage:
 *           type: integer
 *           minimum: 0
 *           description: Current mileage
 *           example: 25000
 *         features:
 *           type: array
 *           items:
 *             type: string
 *           description: Car features
 *           example: ["GPS", "Bluetooth", "Air Conditioning"]
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00.000Z"
 *
 *     Rental:
 *       type: object
 *       required:
 *         - userId
 *         - carId
 *         - startDate
 *         - endDate
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: Unique rental identifier
 *           example: "123e4567-e89b-12d3-a456-************"
 *         userId:
 *           type: string
 *           format: uuid
 *           description: ID of the user renting the car
 *           example: "123e4567-e89b-12d3-a456-************"
 *         carId:
 *           type: string
 *           format: uuid
 *           description: ID of the rented car
 *           example: "123e4567-e89b-12d3-a456-************"
 *         startDate:
 *           type: string
 *           format: date-time
 *           description: Rental start date and time
 *           example: "2024-01-01T10:00:00.000Z"
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: Rental end date and time
 *           example: "2024-01-05T10:00:00.000Z"
 *         totalCost:
 *           type: number
 *           format: float
 *           minimum: 0
 *           description: Total rental cost
 *           example: 199.96
 *         status:
 *           type: string
 *           enum: ["pending", "confirmed", "active", "completed", "cancelled"]
 *           description: Rental status
 *           example: "confirmed"
 *         pickupLocation:
 *           type: string
 *           description: Car pickup location
 *           example: "Downtown Office"
 *         returnLocation:
 *           type: string
 *           description: Car return location
 *           example: "Airport Branch"
 *         notes:
 *           type: string
 *           description: Additional rental notes
 *           example: "Customer requested GPS"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00.000Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T00:00:00.000Z"
 *
 *     CreateUserRequest:
 *       type: object
 *       required:
 *         - email
 *         - name
 *         - phone
 *         - licenseNumber
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         name:
 *           type: string
 *           example: "John Doe"
 *         phone:
 *           type: string
 *           example: "+**********"
 *         dateOfBirth:
 *           type: string
 *           format: date
 *           example: "1990-01-01"
 *         licenseNumber:
 *           type: string
 *           example: "DL123456789"
 *
 *     CreateCarRequest:
 *       type: object
 *       required:
 *         - make
 *         - model
 *         - year
 *         - licensePlate
 *         - pricePerDay
 *       properties:
 *         make:
 *           type: string
 *           example: "Toyota"
 *         model:
 *           type: string
 *           example: "Camry"
 *         year:
 *           type: integer
 *           example: 2023
 *         licensePlate:
 *           type: string
 *           example: "ABC-1234"
 *         color:
 *           type: string
 *           example: "Blue"
 *         fuelType:
 *           type: string
 *           enum: ["gasoline", "diesel", "electric", "hybrid"]
 *           example: "gasoline"
 *         transmission:
 *           type: string
 *           enum: ["manual", "automatic"]
 *           example: "automatic"
 *         seats:
 *           type: integer
 *           example: 5
 *         pricePerDay:
 *           type: number
 *           format: float
 *           example: 49.99
 *         mileage:
 *           type: integer
 *           example: 25000
 *         features:
 *           type: array
 *           items:
 *             type: string
 *           example: ["GPS", "Bluetooth", "Air Conditioning"]
 *
 *     CreateRentalRequest:
 *       type: object
 *       required:
 *         - carId
 *         - startDate
 *         - endDate
 *       properties:
 *         carId:
 *           type: string
 *           format: uuid
 *           example: "123e4567-e89b-12d3-a456-************"
 *         startDate:
 *           type: string
 *           format: date-time
 *           example: "2024-01-01T10:00:00.000Z"
 *         endDate:
 *           type: string
 *           format: date-time
 *           example: "2024-01-05T10:00:00.000Z"
 *         pickupLocation:
 *           type: string
 *           example: "Downtown Office"
 *         returnLocation:
 *           type: string
 *           example: "Airport Branch"
 *         notes:
 *           type: string
 *           example: "Customer requested GPS"
 *
 *     PaginationQuery:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *           description: Page number
 *           example: 1
 *         limit:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *           description: Number of items per page
 *           example: 10
 *         sortBy:
 *           type: string
 *           description: Field to sort by
 *           example: "createdAt"
 *         sortOrder:
 *           type: string
 *           enum: ["asc", "desc"]
 *           default: "desc"
 *           description: Sort order
 *           example: "desc"
 *
 *     PaginatedResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         data:
 *           type: array
 *           items:
 *             type: object
 *           description: Array of items
 *         pagination:
 *           type: object
 *           properties:
 *             page:
 *               type: integer
 *               example: 1
 *             limit:
 *               type: integer
 *               example: 10
 *             total:
 *               type: integer
 *               example: 100
 *             totalPages:
 *               type: integer
 *               example: 10
 *             hasNext:
 *               type: boolean
 *               example: true
 *             hasPrev:
 *               type: boolean
 *               example: false
 *
 *     ValidationError:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: "Validation failed"
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "email"
 *               message:
 *                 type: string
 *                 example: "Invalid email format"
 *         statusCode:
 *           type: number
 *           example: 400
 */

// Export empty object to make this a valid module
export {};