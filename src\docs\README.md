# Lion Car Rentals API Documentation

## Overview

This API provides comprehensive car rental management functionality built with TypeScript, Express.js, and follows SOLID principles for maintainable and scalable code architecture.

## API Documentation

The API documentation is available through Swagger UI at:
- **Development**: `http://localhost:5001/api-docs`
- **Swagger JSON**: `http://localhost:5001/api-docs.json`

## Authentication

The API supports multiple authentication methods:

### Bearer Token (JWT)
```http
Authorization: Bearer <your-jwt-token>
```

### API Key (Optional)
```http
X-API-Key: <your-api-key>
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- **Limit**: 100 requests per 15-minute window
- **Headers**: Rate limit information is provided in response headers
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message",
  "statusCode": 400
}
```

### Validation Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "statusCode": 400
}
```

## Pagination

List endpoints support pagination with the following query parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `sortBy`: Field to sort by
- `sortOrder`: Sort order (`asc` or `desc`, default: `desc`)

### Pagination Response
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## API Endpoints

### Health Check
- `GET /api/health` - Check API health status

### Users (Future Implementation)
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Cars (Future Implementation)
- `GET /api/cars` - List available cars
- `POST /api/cars` - Add new car
- `GET /api/cars/:id` - Get car details
- `PUT /api/cars/:id` - Update car
- `DELETE /api/cars/:id` - Remove car
- `GET /api/cars/search` - Search cars with filters

### Rentals (Future Implementation)
- `GET /api/rentals` - List rentals
- `POST /api/rentals` - Create rental
- `GET /api/rentals/:id` - Get rental details
- `PUT /api/rentals/:id` - Update rental
- `DELETE /api/rentals/:id` - Cancel rental
- `POST /api/rentals/:id/confirm` - Confirm rental
- `POST /api/rentals/:id/complete` - Complete rental

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Unprocessable Entity |
| 429 | Too Many Requests |
| 500 | Internal Server Error |

## Development

### Running the API
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables
```env
# Server Configuration
PORT=5001
NODE_ENV=development

# Database
DATABASE_URL="your-database-connection-string"

# API Security (Optional)
API_KEY="your-api-key"
JWT_SECRET="your-jwt-secret"
```

## Architecture

The API follows SOLID principles:

- **Single Responsibility Principle**: Each class/module has one reason to change
- **Open/Closed Principle**: Open for extension, closed for modification
- **Liskov Substitution Principle**: Derived classes are substitutable for base classes
- **Interface Segregation Principle**: Clients depend only on interfaces they use
- **Dependency Inversion Principle**: Depend on abstractions, not concretions

## Testing

You can test the API using:
- Swagger UI (recommended for interactive testing)
- Postman
- curl commands
- Automated testing tools

### Example curl request
```bash
curl -X GET "http://localhost:5001/api/health" \
  -H "accept: application/json"
```

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: `/api-docs`
- Health Check: `/api/health`