import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";

dotenv.config();

const prisma: PrismaClient = new PrismaClient({
  log: ["warn", "error"],
});

const connectDB = async (): Promise<void> => {
  try {
    // Check if DATABASE_URL is configured
    if (!process.env.DATABASE_URL) {
      console.warn("⚠️  DATABASE_URL not configured. Please set up your database connection in .env file.");
      console.log("📝 Copy .env.example to .env and configure your database URL.");
      return;
    }
    
    await prisma.$connect();
    console.log("✅ Connected to PostgreSQL via Prisma");
  } catch (error: unknown) {
    console.error("❌ Database Connection Error:", error);
    console.log("💡 Make sure your database is running and DATABASE_URL is correct.");
    // Don't exit process in development to allow server to start
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
};

export { prisma, connectDB };
