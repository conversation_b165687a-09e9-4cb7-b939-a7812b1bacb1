import { Request, Response, NextFunction } from 'express';

/**
 * Health check response interface
 * Follows Interface Segregation Principle by defining specific contract
 */
export interface IHealthResponse {
  success: boolean;
  message: string;
  timestamp: string;
  uptime: number;
  environment: string;
  version: string;
}

/**
 * Health controller interface
 * Implements Dependency Inversion Principle
 */
export interface IHealthController {
  checkHealth(req: Request, res: Response, next: NextFunction): Promise<void>;
}

/**
 * Health controller implementation
 * Follows Single Responsibility Principle - handles only health check operations
 */
export class HealthController implements IHealthController {
  /**
   * @swagger
   * /api/health:
   *   get:
   *     tags:
   *       - Health
   *     summary: Check API health status
   *     description: Returns the current health status of the API including uptime and environment information
   *     responses:
   *       200:
   *         description: API is healthy and operational
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "API is healthy and operational"
   *                 timestamp:
   *                   type: string
   *                   format: date-time
   *                   example: "2024-01-01T12:00:00.000Z"
   *                 uptime:
   *                   type: number
   *                   example: 3600
   *                   description: "Server uptime in seconds"
   *                 environment:
   *                   type: string
   *                   example: "development"
   *                 version:
   *                   type: string
   *                   example: "1.0.0"
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  public async checkHealth(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const healthData: IHealthResponse = {
        success: true,
        message: 'API is healthy and operational',
        timestamp: new Date().toISOString(),
        uptime: Math.floor(process.uptime()),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0',
      };

      res.status(200).json(healthData);
    } catch (error) {
      // Pass error to global error handler
      // Follows Open/Closed Principle by delegating error handling
      next(error);
    }
  }
}

/**
 * Factory function for creating health controller instance
 * Implements Dependency Inversion Principle
 */
export const createHealthController = (): IHealthController => {
  return new HealthController();
};

// Export singleton instance for convenience
export const healthController = createHealthController();