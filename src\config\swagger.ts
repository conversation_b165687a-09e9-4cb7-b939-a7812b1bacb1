import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

/**
 * Swagger configuration options following OpenAPI 3.0 specification
 * Implements Single Responsibility Principle by handling only API documentation
 */
const swaggerOptions: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Lion Car Rentals API',
      version: '1.0.0',
      description: 'A comprehensive car rental management system API',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://api.lioncarrentals.com'
          : `http://localhost:${process.env.PORT || 5001}`,
        description: process.env.NODE_ENV === 'production' 
          ? 'Production server'
          : 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-Key',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false,
            },
            message: {
              type: 'string',
              example: 'An error occurred',
            },
            error: {
              type: 'string',
              example: 'Detailed error message',
            },
            statusCode: {
              type: 'number',
              example: 400,
            },
          },
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            message: {
              type: 'string',
              example: 'Operation completed successfully',
            },
            data: {
              type: 'object',
              description: 'Response data',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    './src/routes/*.ts',
    './src/modules/**/*.ts',
    './src/controllers/**/*.ts',
    './src/docs/*.ts',
  ],
};

/**
 * Swagger specification generator
 * Follows Open/Closed Principle - open for extension, closed for modification
 */
export const swaggerSpec = swaggerJSDoc(swaggerOptions);

/**
 * Swagger UI options for better user experience
 * Implements Interface Segregation Principle by providing specific UI configuration
 */
const swaggerUiOptions: swaggerUi.SwaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    docExpansion: 'none',
    filter: true,
    showRequestDuration: true,
    tryItOutEnabled: true,
    requestInterceptor: (req: any) => {
      // Add custom headers or modify requests if needed
      return req;
    },
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #2c3e50; }
    .swagger-ui .scheme-container { background: #f8f9fa; }
  `,
  customSiteTitle: 'Lion Car Rentals API Documentation',
};

/**
 * Swagger setup interface following Dependency Inversion Principle
 * Depends on abstractions (Express interface) rather than concrete implementations
 */
export interface ISwaggerSetup {
  setupSwagger(app: Express): void;
}

/**
 * Swagger setup implementation
 * Follows Single Responsibility Principle by handling only Swagger setup
 */
export class SwaggerSetup implements ISwaggerSetup {
  /**
   * Configure Swagger documentation for Express application
   * @param app Express application instance
   */
  public setupSwagger(app: Express): void {
    try {
      // Serve swagger documentation
      app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, swaggerUiOptions));
      
      // Serve swagger.json for external tools
      app.get('/api-docs.json', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
      });
      
      console.log('📚 Swagger documentation available at /api-docs');
      console.log('📄 Swagger JSON available at /api-docs.json');
    } catch (error) {
      console.error('❌ Failed to setup Swagger documentation:', error);
      // Don't throw error to prevent application crash
      // Follows Liskov Substitution Principle by maintaining expected behavior
    }
  }
}

/**
 * Factory function for creating Swagger setup instance
 * Implements Dependency Inversion Principle
 */
export const createSwaggerSetup = (): ISwaggerSetup => {
  return new SwaggerSetup();
};

/**
 * Health check endpoint schema for Swagger documentation
 */
export const healthCheckSchema = {
  '/api/health': {
    get: {
      tags: ['Health'],
      summary: 'Health check endpoint',
      description: 'Check if the API is running and healthy',
      responses: {
        200: {
          description: 'API is healthy',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: true,
                  },
                  message: {
                    type: 'string',
                    example: 'API is healthy',
                  },
                  timestamp: {
                    type: 'string',
                    format: 'date-time',
                    example: '2024-01-01T00:00:00.000Z',
                  },
                  uptime: {
                    type: 'number',
                    example: 3600,
                    description: 'Server uptime in seconds',
                  },
                },
              },
            },
          },
        },
        500: {
          description: 'Internal server error',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error',
              },
            },
          },
        },
      },
    },
  },
};